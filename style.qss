/* === Modern Design System - Based on Audio Wave Visualizer === */

/* === General Window & Font === */
QWidget {
    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
    background-color: #1e293b; /* Colors.BG_PRIMARY */
    color: #ffffff; /* Colors.TEXT_PRIMARY */
}

/* === Main Window Specific === */
#MainWindowTitle {
    font-size: 24px;
    font-weight: 600;
    color: #ffffff; /* Colors.TEXT_PRIMARY */
}

#MainWindowSubtitle {
    font-size: 14px;
    color: #94a3b8; /* Colors.TEXT_SECONDARY */
}

#SectionTitle {
    font-size: 18px;
    font-weight: 600;
    color: #ffffff; /* Colors.TEXT_PRIMARY */
}

/* === Status Pill === */
#StatusPill {
    background: rgba(30, 41, 59, 200); /* Colors.BG_SURFACE */
    border: 1px solid #475569; /* Colors.NEUTRAL_100 */
    border-radius: 12px;
    padding: 8px 16px;
}

#StatusPill QLabel {
    font-size: 14px;
    color: #ffffff; /* Colors.TEXT_PRIMARY */
}

#StatusDot {
    width: 8px;
    height: 8px;
    border-radius: 4px;
}

#StatusDot[color="#10b981"] {
    background: #10b981; /* Colors.SUCCESS */
}

#StatusDot[color="#64748b"] {
    background: #64748b; /* Colors.TEXT_MUTED */
}

/* === Modern Buttons === */
QPushButton {
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    padding: 8px 16px;
    min-height: 36px;
    border: none;
}

QPushButton[style="primary"] {
    background: #60a5fa; /* Colors.ACCENT_BLUE */
    color: #ffffff; /* Colors.TEXT_PRIMARY */
}

QPushButton[style="primary"]:hover {
    background: #2563eb; /* Colors.INTERACTIVE_HOVER */
}

QPushButton[style="primary"]:pressed {
    background: #1d4ed8; /* Colors.INTERACTIVE_PRESSED */
}

QPushButton[style="primary"]:disabled {
    background: #475569; /* Colors.NEUTRAL_100 */
    color: #64748b; /* Colors.TEXT_MUTED */
}

QPushButton[style="secondary"] {
    background: #10b981; /* Colors.SUCCESS */
    color: #ffffff; /* Colors.TEXT_PRIMARY */
}

QPushButton[style="secondary"]:hover {
    background: #059669; /* Colors.SUCCESS_DARK */
}

QPushButton[style="ghost"] {
    background: transparent;
    color: #94a3b8; /* Colors.TEXT_SECONDARY */
    border: 1px solid #475569; /* Colors.NEUTRAL_100 */
}

QPushButton[style="ghost"]:hover {
    background: rgba(255, 255, 255, 25); /* Colors.OVERLAY_LIGHT */
    color: #ffffff; /* Colors.TEXT_PRIMARY */
}

QPushButton[style="danger"] {
    background: #ef4444; /* Colors.ERROR */
    color: #ffffff; /* Colors.TEXT_PRIMARY */
}

QPushButton[style="danger"]:hover {
    background: #dc2626; /* Colors.ERROR_DARK */
}

/* === Modern Translations List === */
#TranslationsList {
    background: transparent;
    border: none;
}

#TranslationsList::item {
    background: rgba(30, 41, 59, 200); /* Colors.BG_SURFACE */
    border: 1px solid #475569; /* Colors.NEUTRAL_100 */
    border-radius: 12px;
    margin-bottom: 12px;
}

#TranslationsList::item:selected {
    background: #60a5fa; /* Colors.ACCENT_BLUE */
    border-color: #2563eb; /* Colors.INTERACTIVE_HOVER */
}

#TranslationsList::item:hover {
    background: rgba(96, 165, 250, 50); /* Colors.ACCENT_BLUE with alpha */
}

/* === Modern List Item Content === */
#TranslationListItemWidget {
    background: transparent;
    padding: 4px;
}

#OriginalLabel {
    font-weight: 600;
    color: #ffffff; /* Colors.TEXT_PRIMARY */
    font-size: 14px;
}

#TranslatedLabel {
    color: #94a3b8; /* Colors.TEXT_SECONDARY */
    font-size: 14px;
}

#TimeLabel {
    font-size: 12px;
    color: #64748b; /* Colors.TEXT_MUTED */
}

/* === Modern CheckBox === */
QCheckBox {
    color: #94a3b8; /* Colors.TEXT_SECONDARY */
    font-size: 14px;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border-radius: 4px;
    border: 2px solid #475569; /* Colors.NEUTRAL_100 */
    background: #0f172a; /* Colors.BG_SECONDARY */
}

QCheckBox::indicator:checked {
    background: #60a5fa; /* Colors.ACCENT_BLUE */
    border-color: #60a5fa; /* Colors.ACCENT_BLUE */
}

QCheckBox::indicator:hover {
    border-color: #94a3b8; /* Colors.TEXT_SECONDARY */
}

/* === Modern QDialog === */
QDialog {
    background-color: #1e293b; /* Colors.BG_PRIMARY */
    border: 1px solid #475569; /* Colors.NEUTRAL_100 */
    border-radius: 12px;
}

QDialog QLabel {
    color: #ffffff; /* Colors.TEXT_PRIMARY */
}

/* === QStackedWidget === */
QStackedWidget {
    background-color: transparent;
}

/* === Modern QRadioButton === */
QRadioButton {
    color: #ffffff; /* Colors.TEXT_PRIMARY */
    font-size: 14px;
    spacing: 8px;
}

QRadioButton::indicator {
    width: 18px;
    height: 18px;
    border-radius: 9px;
    border: 2px solid #475569; /* Colors.NEUTRAL_100 */
    background: #0f172a; /* Colors.BG_SECONDARY */
}

QRadioButton::indicator:checked {
    background: #60a5fa; /* Colors.ACCENT_BLUE */
    border-color: #60a5fa; /* Colors.ACCENT_BLUE */
}

QRadioButton::indicator:hover {
    border-color: #94a3b8; /* Colors.TEXT_SECONDARY */
}

/* === Modern Input Fields === */
QLineEdit, QTextEdit {
    border: 1px solid #475569; /* Colors.NEUTRAL_100 */
    border-radius: 4px;
    padding: 8px 12px;
    background-color: #0f172a; /* Colors.BG_SECONDARY */
    color: #ffffff; /* Colors.TEXT_PRIMARY */
    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
    font-size: 14px;
    selection-background-color: #60a5fa; /* Colors.ACCENT_BLUE */
}

QLineEdit:focus, QTextEdit:focus {
    border-color: #60a5fa; /* Colors.ACCENT_BLUE */
    outline: none;
}

QLineEdit:disabled, QTextEdit:disabled {
    background-color: #334155; /* Colors.NEUTRAL_200 */
    color: #64748b; /* Colors.TEXT_MUTED */
    border-color: #64748b; /* Colors.TEXT_MUTED */
}

/* === QComboBox === */
QComboBox {
    border: 1px solid #3a3a4a; /* --surface-2 */
    border-radius: 6px;
    padding: 5px;
    background-color: #2a2a3a; /* --surface */
    color: #e0e0e0; /* --text */
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left-width: 1px;
    border-left-color: #3a3a4a;
    border-left-style: solid;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
}

QComboBox::down-arrow {
    image: url(./icons/down_arrow.png); /* Placeholder for an actual arrow icon */
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    border: 1px solid #3a3a4a;
    border-radius: 6px;
    background-color: #2a2a3a;
    selection-background-color: #4a6bdf;
    color: #e0e0e0;
}

/* === QSpinBox === */
QSpinBox {
    border: 1px solid #3a3a4a; /* --surface-2 */
    border-radius: 6px;
    padding: 5px;
    background-color: #2a2a3a; /* --surface */
    color: #e0e0e0; /* --text */
}

QSpinBox::up-button, QSpinBox::down-button {
    width: 20px;
    border: 1px solid #3a3a4a;
    border-radius: 3px;
    background-color: #2a2a3a;
}

QSpinBox::up-arrow, QSpinBox::down-arrow {
    image: url(./icons/up_arrow.png); /* Placeholder */
    width: 10px;
    height: 10px;
}

/* === CustomPromptWindow === */
#CustomPromptWindowContainer {
    background-color: #2a2a3a; /* --surface */
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #3a3a4a; /* --surface-2 */
}

#PromptTextLabel {
    font-size: 16px;
    font-weight: 500;
    color: #e0e0e0; /* --text */
    margin-bottom: 10px;
}

#PromptTextLabel i {
    color: #a0a0a0; /* --text-secondary */
    font-size: 14px;
}

/* === CustomResultWindow === */
#CustomResultWindowContainer {
    background-color: #2a2a3a; /* --surface */
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #3a3a4a; /* --surface-2 */
}

#ResultTitleLabel {
    font-size: 18px;
    font-weight: 600;
    color: #e0e0e0; /* --text */
    margin-bottom: 10px;
}

#ResultOriginalLabel, #ResultTranslationLabel {
    font-size: 14px;
    font-weight: 500;
    color: #e0e0e0; /* --text */
    margin-top: 10px;
    margin-bottom: 5px;
}

#ResultTextEdit {
    background-color: #1e1e2e; /* Slightly darker background for read-only */
    border: 1px solid #3a3a4a;
    border-radius: 6px;
    padding: 8px;
    color: #e0e0e0;
}

#ResultTextEdit:read-only {
    background-color: #1e1e2e; /* Ensure it stays dark */
}

/* Main Floating Action Button (FAB) */
#FloatingActionButton {
    background-color: #5E5DF0; /* A nice purple-blue */
    border-radius: 25px; /* Makes it circular */
    border: none;
    color: white; /* Icon color */
    padding: 10px;
    icon-size: 24px;
}

#FloatingActionButton:hover {
    background-color: #4B4AC4;
}

/* Style for when the service is running */
#FloatingActionButton[active="true"] {
    background-color: #4CAF50; /* Green */
}
#FloatingActionButton[active="true"]:hover {
    background-color: #45A049;
}


/* Container for the expanding action menu */
#ActionMenuContainer {
    background-color: rgba(30, 30, 30, 0.9);
    border-radius: 15px;
    border: 1px solid #444;
}

/* Buttons inside the action menu */
#ActionMenuContainer StyledButton {
    background-color: transparent;
    color: #EAEAEA;
    text-align: left;
    padding: 8px 12px;
    border-radius: 5px;
}

#ActionMenuContainer StyledButton:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* --- Main Containers --- */
#CustomResultWindowContainer {
    background-color: #2D303E; /* Dark slate blue */
    border: 1px solid #4A4E69;
    border-radius: 16px;
    color: #F2E9E4;
}

#ResultTitleLabel {
    color: #FFFFFF;
    font-size: 20px;
    font-weight: bold;
    padding-bottom: 5px;
    border-bottom: 1px solid #4A4E69;
}

/* --- Section Titles (e.g., "Interactive Text", "Selection Details") --- */
#SectionTitle {
    color: #C9ADA7;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    margin-bottom: 5px;
}

/* --- Splitter --- */
QSplitter::handle {
    background: #4A4E69;
}
QSplitter::handle:horizontal {
    width: 2px;
}
QSplitter::handle:vertical {
    height: 2px;
}
QSplitter::handle:hover {
    background: #9A8C98;
}

/* --- Left Pane Info Cards --- */
#InfoCard {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 12px;
}

#InfoCard #SectionSubTitle { /* Title inside a card */
    color: #FFFFFF;
    font-weight: bold;
    font-size: 14px;
}

#InfoCard QLabel { /* Content inside a card */
    color: #DCDCDC;
    font-size: 13px;
}

#InfoCard ul {
    margin-left: -20px; /* Adjust list indentation */
}

/* --- Right Pane Widgets --- */
#InteractiveTextBrowser {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border: none;
    padding: 10px;
}

#DetailsFrame {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 15px;
}

#DetailsFrame QLabel { /* Text in the details box */
    color: #F2E9E4;
    font-size: 14px;
}

#DetailsFrame #DetailsPlaceholderLabel {
    color: #999999;
    font-style: italic;
}

/* --- Buttons (Inherited from StyledButton, but can be customized) --- */
StyledButton[style="primary"] {
    background-color: #5E5DF0;
    color: white;
    font-weight: bold;
    border: none;
    border-radius: 8px;
    padding: 10px 18px;
}
StyledButton[style="primary"]:hover {
    background-color: #4B4AC4;
}

StyledButton[style="ghost"] {
    background-color: transparent;
    color: #C9ADA7;
    border: 1px solid #4A4E69;
    border-radius: 8px;
    padding: 10px 18px;
}
StyledButton[style="ghost"]:hover {
    background-color: #4A4E69;
    color: white;
}

/* Prompt Window Styling */
#CustomPromptWindowContainer {
    background-color: rgba(40, 40, 40, 0.95);
    border: 1px solid #666;
    border-radius: 15px;
    color: white;
}

#InteractiveTextFrame, #DetailsFrame {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 10px;
}

#ClickableWordLabel {
    color: #EAEAEA;
    font-size: 18px;
    font-weight: bold;
    padding: 2px 4px;
    border-radius: 4px;
}

#ClickableWordLabel:hover {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

#DetailsPlaceholderLabel {
    color: #999999;
    font-style: italic;
}

#DetailsFrame QLabel {
    color: #DDDDDD;
    font-size: 14px;
}

/* ===== MODERN FLOATING UI STYLES ===== */

/* Modern Floating Action Button */
#FloatingActionButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #0ea5e9, stop:1 #0284c7);
    border: 2px solid #0369a1;
    border-radius: 25px;
    color: white;
    font-weight: 600;
    font-size: 16px;
}

#FloatingActionButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #0284c7, stop:1 #0369a1);
    border-color: #075985;
    transform: scale(1.05);
}

#FloatingActionButton:pressed {
    background: #0369a1;
    transform: scale(0.95);
}

/* Modern Action Menu Container */
#ModernActionMenuContainer {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #1e293b, stop:1 #334155);
    border: 1px solid #475569;
    border-radius: 12px;
    padding: 8px;
}

/* Menu Section Labels */
#MenuSectionLabel {
    color: #0ea5e9;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 8px 0 4px 0;
    padding: 0 4px;
}

/* Menu Buttons */
#MenuButton {
    background: transparent;
    color: #cbd5e1;
    border: 1px solid transparent;
    border-radius: 6px;
    padding: 8px 12px;
    text-align: left;
    font-size: 13px;
    min-height: 16px;
}

#MenuButton:hover {
    background: rgba(14, 165, 233, 0.1);
    border-color: rgba(14, 165, 233, 0.3);
    color: #f8fafc;
}

#MenuButton:pressed {
    background: rgba(14, 165, 233, 0.2);
}

/* Menu Separators */
#MenuSeparator {
    background: #475569;
    border: none;
    height: 1px;
    margin: 8px 0;
}

/* ===== QUICK ACCESS PANEL STYLES ===== */

/* Quick Access Panel Container */
#QuickAccessPanelContainer {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #1e293b, stop:1 #334155);
    border: 1px solid #475569;
    border-radius: 12px;
}

/* Panel Toggle Button */
#PanelToggleButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #0ea5e9, stop:1 #0284c7);
    border: none;
    border-radius: 20px;
    color: white;
    font-weight: 600;
    font-size: 18px;
}

#PanelToggleButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #0284c7, stop:1 #0369a1);
    transform: scale(1.05);
}

/* Service Indicator */
#ServiceIndicator {
    font-size: 16px;
    font-weight: bold;
}

/* Feature Buttons */
#FeatureButton {
    background: transparent;
    color: #cbd5e1;
    border: 1px solid transparent;
    border-radius: 8px;
    font-size: 16px;
}

#FeatureButton:hover {
    background: rgba(14, 165, 233, 0.1);
    border-color: rgba(14, 165, 233, 0.3);
    color: #f8fafc;
    transform: scale(1.1);
}

#FeatureButton:pressed {
    background: rgba(14, 165, 233, 0.2);
    transform: scale(0.95);
}

/* Feature Buttons Container */
#FeatureButtonsContainer {
    background: transparent;
}

/* === Modern Scrollbars === */
QScrollBar:vertical {
    background: rgba(30, 41, 59, 100);
    width: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background: #475569; /* Colors.NEUTRAL_100 */
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background: #60a5fa; /* Colors.ACCENT_BLUE */
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    border: none;
    background: none;
    height: 0;
}

QScrollBar:horizontal {
    background: rgba(30, 41, 59, 100);
    height: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:horizontal {
    background: #475569; /* Colors.NEUTRAL_100 */
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background: #60a5fa; /* Colors.ACCENT_BLUE */
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    border: none;
    background: none;
    width: 0;
}

/* === Modern List Widgets === */
QListWidget {
    background: rgba(30, 41, 59, 200); /* Colors.BG_SURFACE */
    border: 1px solid #475569; /* Colors.NEUTRAL_100 */
    border-radius: 12px;
    padding: 12px;
}

QListWidget::item {
    background: transparent;
    border: none;
    padding: 12px;
    border-radius: 8px;
    color: #ffffff; /* Colors.TEXT_PRIMARY */
}

QListWidget::item:selected {
    background: #60a5fa; /* Colors.ACCENT_BLUE */
    color: #ffffff; /* Colors.TEXT_PRIMARY */
}

QListWidget::item:hover {
    background: rgba(255, 255, 255, 25); /* Colors.OVERLAY_LIGHT */
}