#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Design System Migration Test

This script tests the migrated UI components to ensure they work correctly
with the new design system based on the audio wave visualizer.
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PySide6.QtCore import Qt

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from ui.design_system import Colors, Typography, Spacing, ComponentStyles
    from ui.color_palette import ModernColorPalette, ColorRole, palette
    from ui.widgets import StyledButton, StatusPill
    print("✅ All imports successful")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


class DesignTestWindow(QMainWindow):
    """Test window to showcase the new design system."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Design System Migration Test")
        self.setMinimumSize(800, 600)
        
        # Apply the new design system background
        self.setStyleSheet(f"""
            QMainWindow {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {Colors.BG_PRIMARY.name()},
                    stop:1 {Colors.BG_SECONDARY.name()});
                color: {Colors.TEXT_PRIMARY.name()};
            }}
        """)
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the test UI."""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(Spacing.CONTAINER, Spacing.CONTAINER, Spacing.CONTAINER, Spacing.CONTAINER)
        layout.setSpacing(Spacing.SECTION)
        
        # Title
        title = QLabel("🎨 Design System Migration Test")
        title.setFont(Typography.create_font(Typography.SIZE_TITLE, Typography.WEIGHT_BOLD))
        title.setStyleSheet(f"color: {Colors.TEXT_PRIMARY.name()}; margin-bottom: {Spacing.LG}px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Subtitle
        subtitle = QLabel("Testing the beautiful new design system based on the audio wave visualizer")
        subtitle.setFont(Typography.create_font(Typography.SIZE_BODY))
        subtitle.setStyleSheet(f"color: {Colors.TEXT_SECONDARY.name()}; margin-bottom: {Spacing.XL}px;")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setWordWrap(True)
        layout.addWidget(subtitle)
        
        # Status Pills Section
        status_section = QWidget()
        status_section.setStyleSheet(ComponentStyles.get_panel_style())
        status_layout = QVBoxLayout(status_section)
        status_layout.setContentsMargins(Spacing.PANEL, Spacing.PANEL, Spacing.PANEL, Spacing.PANEL)
        status_layout.setSpacing(Spacing.MD)
        
        status_title = QLabel("Status Pills")
        status_title.setFont(Typography.create_font(Typography.SIZE_HEADING, Typography.WEIGHT_BOLD))
        status_title.setStyleSheet(f"color: {Colors.TEXT_PRIMARY.name()};")
        status_layout.addWidget(status_title)
        
        # Test different status pills
        active_pill = StatusPill("Service Active", "active")
        inactive_pill = StatusPill("Service Inactive", "inactive")
        error_pill = StatusPill("Service Error", "error")
        
        status_layout.addWidget(active_pill)
        status_layout.addWidget(inactive_pill)
        status_layout.addWidget(error_pill)
        
        layout.addWidget(status_section)
        
        # Buttons Section
        buttons_section = QWidget()
        buttons_section.setStyleSheet(ComponentStyles.get_panel_style())
        buttons_layout = QVBoxLayout(buttons_section)
        buttons_layout.setContentsMargins(Spacing.PANEL, Spacing.PANEL, Spacing.PANEL, Spacing.PANEL)
        buttons_layout.setSpacing(Spacing.MD)
        
        buttons_title = QLabel("Styled Buttons")
        buttons_title.setFont(Typography.create_font(Typography.SIZE_HEADING, Typography.WEIGHT_BOLD))
        buttons_title.setStyleSheet(f"color: {Colors.TEXT_PRIMARY.name()};")
        buttons_layout.addWidget(buttons_title)
        
        # Test different button styles
        primary_btn = StyledButton("Primary Button", "primary")
        secondary_btn = StyledButton("Secondary Button", "secondary")
        danger_btn = StyledButton("Danger Button", "danger")
        ghost_btn = StyledButton("Ghost Button", "ghost")
        
        buttons_layout.addWidget(primary_btn)
        buttons_layout.addWidget(secondary_btn)
        buttons_layout.addWidget(danger_btn)
        buttons_layout.addWidget(ghost_btn)
        
        layout.addWidget(buttons_section)
        
        # Color Palette Section
        palette_section = QWidget()
        palette_section.setStyleSheet(ComponentStyles.get_panel_style())
        palette_layout = QVBoxLayout(palette_section)
        palette_layout.setContentsMargins(Spacing.PANEL, Spacing.PANEL, Spacing.PANEL, Spacing.PANEL)
        palette_layout.setSpacing(Spacing.MD)
        
        palette_title = QLabel("Color Palette")
        palette_title.setFont(Typography.create_font(Typography.SIZE_HEADING, Typography.WEIGHT_BOLD))
        palette_title.setStyleSheet(f"color: {Colors.TEXT_PRIMARY.name()};")
        palette_layout.addWidget(palette_title)
        
        # Show color examples
        color_info = QLabel(f"""
        Primary: {Colors.ACCENT_BLUE.name()}
        Success: {Colors.SUCCESS.name()}
        Error: {Colors.ERROR.name()}
        Background: {Colors.BG_PRIMARY.name()}
        Surface: {Colors.BG_SURFACE.name()}
        """)
        color_info.setFont(Typography.create_font(Typography.SIZE_BODY))
        color_info.setStyleSheet(f"color: {Colors.TEXT_SECONDARY.name()}; font-family: monospace;")
        palette_layout.addWidget(color_info)
        
        layout.addWidget(palette_section)
        
        # Success message
        success_label = QLabel("✅ Design system migration successful!")
        success_label.setFont(Typography.create_font(Typography.SIZE_HEADING, Typography.WEIGHT_BOLD))
        success_label.setStyleSheet(f"color: {Colors.SUCCESS.name()}; margin-top: {Spacing.XL}px;")
        success_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(success_label)
        
        layout.addStretch()


def main():
    """Main test function."""
    print("🧪 Starting Design System Migration Test...")
    
    app = QApplication(sys.argv)
    
    # Load the main stylesheet
    try:
        with open('style.qss', 'r', encoding='utf-8') as f:
            app.setStyleSheet(f.read())
        print("✅ Stylesheet loaded successfully")
    except FileNotFoundError:
        print("⚠️  Warning: style.qss not found, using component styles only")
    
    # Create and show the test window
    window = DesignTestWindow()
    window.show()
    
    print("✅ Test window created and displayed")
    print("🎨 Design system migration test is running...")
    print("   - Check the window for visual validation")
    print("   - All components should use the new beautiful design")
    print("   - Colors should match the audio wave visualizer aesthetic")
    
    # Run the application
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
