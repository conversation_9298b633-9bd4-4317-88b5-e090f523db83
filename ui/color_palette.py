# -*- coding: utf-8 -*-
"""
Modern Color Palette System

This module provides a comprehensive color palette system based on the beautiful
audio wave visualizer design. It includes semantic color naming, usage guidelines,
and utility functions for consistent color application across the application.

Features:
- Semantic color naming (primary, secondary, success, error, etc.)
- Dark theme optimized colors
- Accessibility considerations
- Color utility functions
- Usage guidelines and examples
"""

from PySide6.QtGui import QColor
from typing import Dict, <PERSON><PERSON>, Union
from enum import Enum


class ColorRole(Enum):
    """Semantic color roles for consistent usage."""
    PRIMARY = "primary"
    SECONDARY = "secondary"
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"
    SURFACE = "surface"
    BACKGROUND = "background"
    TEXT = "text"
    ACCENT = "accent"


class ModernColorPalette:
    """
    Modern color palette based on the audio wave visualizer design.
    
    This palette is designed for dark themes and provides excellent contrast
    and visual hierarchy while maintaining the beautiful aesthetic of the
    audio wave visualizer.
    """
    
    # === Primary Colors ===
    # Main brand colors derived from the audio visualizer
    PRIMARY_50 = QColor(239, 246, 255)   # #eff6ff - Very light blue
    PRIMARY_100 = QColor(219, 234, 254)  # #dbeafe - Light blue
    PRIMARY_200 = QColor(191, 219, 254)  # #bfdbfe - Medium light blue
    PRIMARY_300 = QColor(147, 197, 253)  # #93c5fd - Medium blue
    PRIMARY_400 = QColor(96, 165, 250)   # #60a5fa - Main accent blue
    PRIMARY_500 = QColor(59, 130, 246)   # #3b82f6 - Primary blue
    PRIMARY_600 = QColor(37, 99, 235)    # #2563eb - Dark blue
    PRIMARY_700 = QColor(29, 78, 216)    # #1d4ed8 - Darker blue
    PRIMARY_800 = QColor(30, 64, 175)    # #1e40af - Very dark blue
    PRIMARY_900 = QColor(30, 58, 138)    # #1e3a8a - Darkest blue
    
    # === Secondary Colors ===
    # Purple accent colors from the visualizer
    SECONDARY_50 = QColor(245, 243, 255)   # #f5f3ff - Very light purple
    SECONDARY_100 = QColor(237, 233, 254)  # #ede9fe - Light purple
    SECONDARY_200 = QColor(221, 214, 254)  # #ddd6fe - Medium light purple
    SECONDARY_300 = QColor(196, 181, 253)  # #c4b5fd - Medium purple
    SECONDARY_400 = QColor(167, 139, 250)  # #a78bfa - Light accent purple
    SECONDARY_500 = QColor(139, 92, 246)   # #8b5cf6 - Main purple
    SECONDARY_600 = QColor(124, 58, 237)   # #7c3aed - Dark purple
    SECONDARY_700 = QColor(109, 40, 217)   # #6d28d9 - Darker purple
    SECONDARY_800 = QColor(91, 33, 182)    # #5b21b6 - Very dark purple
    SECONDARY_900 = QColor(76, 29, 149)    # #4c1d95 - Darkest purple
    
    # === Success Colors ===
    # Green colors for success states and transcribed segments
    SUCCESS_50 = QColor(236, 253, 245)    # #ecfdf5 - Very light green
    SUCCESS_100 = QColor(209, 250, 229)   # #d1fae5 - Light green
    SUCCESS_200 = QColor(167, 243, 208)   # #a7f3d0 - Medium light green
    SUCCESS_300 = QColor(110, 231, 183)   # #6ee7b7 - Medium green
    SUCCESS_400 = QColor(52, 211, 153)    # #34d399 - Light accent green
    SUCCESS_500 = QColor(16, 185, 129)    # #10b981 - Main success green
    SUCCESS_600 = QColor(5, 150, 105)     # #059669 - Dark green
    SUCCESS_700 = QColor(4, 120, 87)      # #047857 - Darker green
    SUCCESS_800 = QColor(6, 95, 70)       # #065f46 - Very dark green
    SUCCESS_900 = QColor(6, 78, 59)       # #064e3b - Darkest green
    
    # === Error Colors ===
    # Red colors for error states and playhead
    ERROR_50 = QColor(254, 242, 242)      # #fef2f2 - Very light red
    ERROR_100 = QColor(254, 226, 226)     # #fee2e2 - Light red
    ERROR_200 = QColor(254, 202, 202)     # #fecaca - Medium light red
    ERROR_300 = QColor(252, 165, 165)     # #fca5a5 - Medium red
    ERROR_400 = QColor(248, 113, 113)     # #f87171 - Light accent red
    ERROR_500 = QColor(239, 68, 68)       # #ef4444 - Main error red
    ERROR_600 = QColor(220, 38, 38)       # #dc2626 - Dark red
    ERROR_700 = QColor(185, 28, 28)       # #b91c1c - Darker red
    ERROR_800 = QColor(153, 27, 27)       # #991b1b - Very dark red
    ERROR_900 = QColor(127, 29, 29)       # #7f1d1d - Darkest red
    
    # === Warning Colors ===
    # Amber colors for warning states
    WARNING_50 = QColor(255, 251, 235)    # #fffbeb - Very light amber
    WARNING_100 = QColor(254, 243, 199)   # #fef3c7 - Light amber
    WARNING_200 = QColor(253, 230, 138)   # #fde68a - Medium light amber
    WARNING_300 = QColor(252, 211, 77)    # #fcd34d - Medium amber
    WARNING_400 = QColor(251, 191, 36)    # #fbbf24 - Light accent amber
    WARNING_500 = QColor(245, 158, 11)    # #f59e0b - Main warning amber
    WARNING_600 = QColor(217, 119, 6)     # #d97706 - Dark amber
    WARNING_700 = QColor(180, 83, 9)      # #b45309 - Darker amber
    WARNING_800 = QColor(146, 64, 14)     # #92400e - Very dark amber
    WARNING_900 = QColor(120, 53, 15)     # #78350f - Darkest amber
    
    # === Neutral Colors ===
    # Slate colors for backgrounds and surfaces
    NEUTRAL_50 = QColor(248, 250, 252)    # #f8fafc - Very light slate
    NEUTRAL_100 = QColor(241, 245, 249)   # #f1f5f9 - Light slate
    NEUTRAL_200 = QColor(226, 232, 240)   # #e2e8f0 - Medium light slate
    NEUTRAL_300 = QColor(203, 213, 225)   # #cbd5e1 - Medium slate
    NEUTRAL_400 = QColor(148, 163, 184)   # #94a3b8 - Light neutral
    NEUTRAL_500 = QColor(100, 116, 139)   # #64748b - Main neutral
    NEUTRAL_600 = QColor(71, 85, 105)     # #475569 - Dark neutral
    NEUTRAL_700 = QColor(51, 65, 85)      # #334155 - Darker neutral
    NEUTRAL_800 = QColor(30, 41, 59)      # #1e293b - Very dark neutral
    NEUTRAL_900 = QColor(15, 23, 42)      # #0f172a - Darkest neutral
    
    # === Semantic Color Mappings ===
    # Map semantic roles to specific colors for easy usage
    
    @classmethod
    def get_color(cls, role: ColorRole, shade: int = 500) -> QColor:
        """
        Get a color by semantic role and shade.
        
        Args:
            role: The semantic color role
            shade: The color shade (50-900, default 500)
            
        Returns:
            QColor object for the requested color
        """
        color_map = {
            ColorRole.PRIMARY: {
                50: cls.PRIMARY_50, 100: cls.PRIMARY_100, 200: cls.PRIMARY_200,
                300: cls.PRIMARY_300, 400: cls.PRIMARY_400, 500: cls.PRIMARY_500,
                600: cls.PRIMARY_600, 700: cls.PRIMARY_700, 800: cls.PRIMARY_800,
                900: cls.PRIMARY_900
            },
            ColorRole.SECONDARY: {
                50: cls.SECONDARY_50, 100: cls.SECONDARY_100, 200: cls.SECONDARY_200,
                300: cls.SECONDARY_300, 400: cls.SECONDARY_400, 500: cls.SECONDARY_500,
                600: cls.SECONDARY_600, 700: cls.SECONDARY_700, 800: cls.SECONDARY_800,
                900: cls.SECONDARY_900
            },
            ColorRole.SUCCESS: {
                50: cls.SUCCESS_50, 100: cls.SUCCESS_100, 200: cls.SUCCESS_200,
                300: cls.SUCCESS_300, 400: cls.SUCCESS_400, 500: cls.SUCCESS_500,
                600: cls.SUCCESS_600, 700: cls.SUCCESS_700, 800: cls.SUCCESS_800,
                900: cls.SUCCESS_900
            },
            ColorRole.ERROR: {
                50: cls.ERROR_50, 100: cls.ERROR_100, 200: cls.ERROR_200,
                300: cls.ERROR_300, 400: cls.ERROR_400, 500: cls.ERROR_500,
                600: cls.ERROR_600, 700: cls.ERROR_700, 800: cls.ERROR_800,
                900: cls.ERROR_900
            },
            ColorRole.WARNING: {
                50: cls.WARNING_50, 100: cls.WARNING_100, 200: cls.WARNING_200,
                300: cls.WARNING_300, 400: cls.WARNING_400, 500: cls.WARNING_500,
                600: cls.WARNING_600, 700: cls.WARNING_700, 800: cls.WARNING_800,
                900: cls.WARNING_900
            }
        }
        
        if role in color_map and shade in color_map[role]:
            return color_map[role][shade]
        
        # Fallback to primary color
        return cls.PRIMARY_500
    
    # === Quick Access Properties ===
    # Common colors for easy access
    
    @property
    def background_primary(self) -> QColor:
        """Primary background color (dark slate)."""
        return self.NEUTRAL_800
    
    @property
    def background_secondary(self) -> QColor:
        """Secondary background color (darker slate)."""
        return self.NEUTRAL_900
    
    @property
    def surface(self) -> QColor:
        """Surface color for panels and cards."""
        return QColor(30, 41, 59, 200)  # NEUTRAL_800 with alpha
    
    @property
    def text_primary(self) -> QColor:
        """Primary text color (white)."""
        return QColor(255, 255, 255)
    
    @property
    def text_secondary(self) -> QColor:
        """Secondary text color (light slate)."""
        return self.NEUTRAL_400
    
    @property
    def text_muted(self) -> QColor:
        """Muted text color (medium slate)."""
        return self.NEUTRAL_500
    
    @property
    def accent_primary(self) -> QColor:
        """Primary accent color (blue)."""
        return self.PRIMARY_400
    
    @property
    def accent_secondary(self) -> QColor:
        """Secondary accent color (purple)."""
        return self.SECONDARY_400


# Create a global instance for easy access
palette = ModernColorPalette()

# Export commonly used colors for backward compatibility
Colors = type('Colors', (), {
    'BG_PRIMARY': palette.background_primary,
    'BG_SECONDARY': palette.background_secondary,
    'BG_SURFACE': palette.surface,
    'TEXT_PRIMARY': palette.text_primary,
    'TEXT_SECONDARY': palette.text_secondary,
    'TEXT_MUTED': palette.text_muted,
    'ACCENT_BLUE': palette.accent_primary,
    'ACCENT_PURPLE': palette.accent_secondary,
    'SUCCESS': palette.get_color(ColorRole.SUCCESS),
    'SUCCESS_DARK': palette.get_color(ColorRole.SUCCESS, 600),
    'ERROR': palette.get_color(ColorRole.ERROR),
    'ERROR_DARK': palette.get_color(ColorRole.ERROR, 600),
    'WARNING': palette.get_color(ColorRole.WARNING),
    'NEUTRAL_100': palette.NEUTRAL_600,
    'NEUTRAL_200': palette.NEUTRAL_700,
    'NEUTRAL_300': palette.NEUTRAL_800,
    'SELECTION_FILL': QColor(99, 102, 241, 51),
    'TRANSCRIBED_FILL': QColor(16, 185, 129, 100),
    'OVERLAY_LIGHT': QColor(255, 255, 255, 25),
    'OVERLAY_DARK': QColor(0, 0, 0, 51),
    'INTERACTIVE_HOVER': palette.get_color(ColorRole.PRIMARY, 600),
    'INTERACTIVE_PRESSED': palette.get_color(ColorRole.PRIMARY, 700),
})


# Usage guidelines and examples
USAGE_GUIDELINES = """
Color Usage Guidelines:

1. Background Colors:
   - Use palette.background_primary for main backgrounds
   - Use palette.background_secondary for deeper backgrounds
   - Use palette.surface for panels, cards, and elevated surfaces

2. Text Colors:
   - Use palette.text_primary for main content
   - Use palette.text_secondary for supporting text
   - Use palette.text_muted for less important text

3. Accent Colors:
   - Use palette.accent_primary for primary actions and highlights
   - Use palette.accent_secondary for secondary accents
   - Use semantic colors (success, error, warning) for status indicators

4. Interactive States:
   - Use darker shades for hover states
   - Use even darker shades for pressed states
   - Maintain sufficient contrast for accessibility

Example Usage:
    # Get a specific color
    primary_color = palette.get_color(ColorRole.PRIMARY, 500)
    
    # Use quick access properties
    bg_color = palette.background_primary
    text_color = palette.text_primary
    
    # Use semantic colors
    success_color = palette.get_color(ColorRole.SUCCESS)
    error_color = palette.get_color(ColorRole.ERROR)
"""

__all__ = ['ModernColorPalette', 'ColorRole', 'palette', 'Colors', 'USAGE_GUIDELINES']
