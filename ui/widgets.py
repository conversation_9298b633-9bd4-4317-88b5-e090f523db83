
from PySide6.QtWidgets import <PERSON><PERSON>rame, QLabel, QHBoxLayout, QListWidgetItem, QWidget, QVBoxLayout, QPushButton
from PySide6.QtCore import Qt
from ui.design_system import Colors, Typography, Spacing

class StatusPill(QFrame):
    def __init__(self, text, status="ready"):
        super().__init__()
        self.setObjectName("StatusPill")
        self.status = status

        layout = QHBoxLayout(self)
        layout.setContentsMargins(Spacing.SM, Spacing.SM, Spacing.SM, Spacing.SM)
        layout.setSpacing(Spacing.SM)

        self.dot = QLabel("●")
        self.dot.setObjectName("StatusDot")
        self.dot.setFixedSize(12, 12)
        self.dot.setAlignment(Qt.AlignCenter)

        self.label = QLabel(text)
        self.label.setFont(Typography.create_font(Typography.SIZE_BODY, Typography.WEIGHT_MEDIUM))

        layout.addWidget(self.dot)
        layout.addWidget(self.label)

        self.update_status_styling()

    def update_status_styling(self):
        """Update styling based on status."""
        status_colors = {
            "ready": Colors.TEXT_SECONDARY.name(),
            "active": Colors.SUCCESS.name(),
            "inactive": Colors.TEXT_MUTED.name(),
            "error": Colors.ERROR.name()
        }

        color = status_colors.get(self.status, Colors.TEXT_SECONDARY.name())
        self.dot.setStyleSheet(f"color: {color}; font-size: 12px;")

        # Set the property for QSS styling
        if self.status == "active":
            self.dot.setProperty("color", Colors.SUCCESS.name())
        else:
            self.dot.setProperty("color", Colors.TEXT_MUTED.name())

    def set_status(self, status: str):
        """Update the status."""
        self.status = status
        self.update_status_styling()

class TranslationListItem(QListWidgetItem):
    def __init__(self, original, translated, timestamp):
        super().__init__()
        self.widget = QWidget()
        self.widget.setObjectName("TranslationListItemWidget")
        layout = QVBoxLayout(self.widget)
        layout.setContentsMargins(Spacing.LG, Spacing.MD, Spacing.LG, Spacing.MD)
        layout.setSpacing(Spacing.XS)

        # Original text label
        original_label = QLabel(original)
        original_label.setObjectName("OriginalLabel")
        original_label.setFont(Typography.create_font(Typography.SIZE_BODY, Typography.WEIGHT_BOLD))
        original_label.setWordWrap(True)

        # Translated text label
        translated_label = QLabel(translated)
        translated_label.setObjectName("TranslatedLabel")
        translated_label.setFont(Typography.create_font(Typography.SIZE_BODY))
        translated_label.setWordWrap(True)

        # Timestamp label
        time_label = QLabel(timestamp)
        time_label.setObjectName("TimeLabel")
        time_label.setFont(Typography.create_font(Typography.SIZE_CAPTION))

        layout.addWidget(original_label)
        layout.addWidget(translated_label)
        layout.addWidget(time_label)

        self.setSizeHint(self.widget.sizeHint())

class StyledButton(QPushButton):
    def __init__(self, text, style="primary"):
        super().__init__(text)
        self.setProperty("style", style)
        self.setFont(Typography.create_font(Typography.SIZE_BODY, Typography.WEIGHT_BOLD))
        self.setCursor(Qt.PointingHandCursor)

        # Apply modern styling
        self.setMinimumHeight(36)
        self.setStyleSheet(self._get_style_for_variant(style))

    def _get_style_for_variant(self, variant: str) -> str:
        """Get the appropriate stylesheet for the button variant."""
        base_style = """
            QPushButton {
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: 600;
            }
        """

        if variant == "primary":
            return base_style + f"""
                QPushButton {{
                    background-color: {Colors.ACCENT_BLUE.name()};
                    color: {Colors.TEXT_PRIMARY.name()};
                }}
                QPushButton:hover {{
                    background-color: {Colors.INTERACTIVE_HOVER.name()};
                }}
                QPushButton:pressed {{
                    background-color: {Colors.INTERACTIVE_PRESSED.name()};
                }}
                QPushButton:disabled {{
                    background-color: {Colors.NEUTRAL_100.name()};
                    color: {Colors.TEXT_MUTED.name()};
                }}
            """
        elif variant == "secondary":
            return base_style + f"""
                QPushButton {{
                    background-color: {Colors.SUCCESS.name()};
                    color: {Colors.TEXT_PRIMARY.name()};
                }}
                QPushButton:hover {{
                    background-color: {Colors.SUCCESS_DARK.name()};
                }}
                QPushButton:disabled {{
                    background-color: {Colors.NEUTRAL_100.name()};
                    color: {Colors.TEXT_MUTED.name()};
                }}
            """
        elif variant == "danger":
            return base_style + f"""
                QPushButton {{
                    background-color: {Colors.ERROR.name()};
                    color: {Colors.TEXT_PRIMARY.name()};
                }}
                QPushButton:hover {{
                    background-color: {Colors.ERROR_DARK.name()};
                }}
                QPushButton:disabled {{
                    background-color: {Colors.NEUTRAL_100.name()};
                    color: {Colors.TEXT_MUTED.name()};
                }}
            """
        elif variant == "ghost":
            return base_style + f"""
                QPushButton {{
                    background-color: transparent;
                    color: {Colors.TEXT_SECONDARY.name()};
                    border: 1px solid {Colors.NEUTRAL_100.name()};
                }}
                QPushButton:hover {{
                    background-color: {Colors.OVERLAY_LIGHT.name()};
                    color: {Colors.TEXT_PRIMARY.name()};
                }}
                QPushButton:disabled {{
                    color: {Colors.TEXT_MUTED.name()};
                    border-color: {Colors.TEXT_MUTED.name()};
                }}
            """

        return base_style
