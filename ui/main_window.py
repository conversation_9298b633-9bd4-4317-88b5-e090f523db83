import sys
import os
from PySide6.QtWidgets import (
    QApp<PERSON>, QMainWindow, QStyle, QWidget, QLabel, QCheckBox,
    QVBoxLayout, QHBoxLayout, QListWidget, QFrame,
    QMessageBox, QSystemTrayIcon, QMenu
)
from PySide6.QtCore import Qt, QTimer, Slot
from PySide6.QtGui import QAction

# Local imports
from manager.config_manager import ConfigManager
from manager.cache_manager import CacheManager
from services.clipboard.clipboard_service import ClipboardService
from ui.settings_window import SettingsWindow
from ui.widgets import StatusPill, TranslationListItem, StyledButton
from ui.design_system import Colors, Typography, Spacing, ComponentStyles

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Clipboard Translator")
        self.setMinimumSize(600, 700)

        # Apply modern styling
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {Colors.BG_PRIMARY.name()};
                color: {Colors.TEXT_PRIMARY.name()};
            }}
        """)

        # Managers
        self.config_manager = ConfigManager(os.path.expanduser("~/.clipboard_translator_config.json"))
        self.cache_manager = CacheManager(os.path.expanduser("~/.clipboard_translator_cache.db"))
        self.clipboard_service = None
        self.prompt_window = None
        self.result_window = None

        # Setup system tray
        self.setup_system_tray()

        # Create main UI
        self.setup_ui()

        # Auto-start if configured
        if self.config_manager.get('auto_start', False):
            QTimer.singleShot(100, self.start_service)

    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(24, 24, 24, 24)
        main_layout.setSpacing(24)

        # Modern Header
        header = QWidget()
        header.setStyleSheet(ComponentStyles.get_panel_style())
        header_layout = QVBoxLayout(header)
        header_layout.setContentsMargins(Spacing.PANEL, Spacing.PANEL, Spacing.PANEL, Spacing.PANEL)
        header_layout.setSpacing(Spacing.XS)

        title = QLabel("Clipboard Translator")
        title.setFont(Typography.create_font(Typography.SIZE_TITLE, Typography.WEIGHT_BOLD))
        title.setStyleSheet(f"color: {Colors.TEXT_PRIMARY.name()};")

        subtitle = QLabel("Instantly translate what you copy with modern AI.")
        subtitle.setFont(Typography.create_font(Typography.SIZE_BODY))
        subtitle.setStyleSheet(f"color: {Colors.TEXT_SECONDARY.name()};")

        header_layout.addWidget(title)
        header_layout.addWidget(subtitle)
        main_layout.addWidget(header)

        # Modern Status section
        status_section = QWidget()
        status_section.setStyleSheet(ComponentStyles.get_panel_style())
        status_layout = QHBoxLayout(status_section)
        status_layout.setContentsMargins(Spacing.PANEL, Spacing.PANEL, Spacing.PANEL, Spacing.PANEL)
        status_layout.setSpacing(Spacing.MD)

        self.status_pill = StatusPill("Service Status: Checking...")

        self.start_btn = StyledButton("Start Service", "primary")
        self.start_btn.clicked.connect(self.start_service)

        self.stop_btn = StyledButton("Stop Service", "danger")
        self.stop_btn.clicked.connect(self.stop_service)
        self.stop_btn.setEnabled(False)

        status_layout.addWidget(self.status_pill)
        status_layout.addStretch()
        status_layout.addWidget(self.start_btn)
        status_layout.addWidget(self.stop_btn)
        main_layout.addWidget(status_section)

        # Translations list
        translations_section = QWidget()
        translations_layout = QVBoxLayout(translations_section)
        translations_layout.setContentsMargins(0, 0, 0, 0)
        translations_layout.setSpacing(16)

        translations_title = QLabel("Recent Translations")
        translations_title.setObjectName("SectionTitle")

        self.translations_list = QListWidget()
        self.translations_list.setObjectName("TranslationsList")

        refresh_btn = StyledButton("Refresh", "ghost")
        refresh_btn.clicked.connect(self.refresh_recent_translations)

        translations_layout.addWidget(translations_title)
        translations_layout.addWidget(self.translations_list)
        translations_layout.addWidget(refresh_btn, 0, Qt.AlignLeft)
        main_layout.addWidget(translations_section)

        # Footer
        footer = QWidget()
        footer_layout = QHBoxLayout(footer)
        footer_layout.setContentsMargins(0, 0, 0, 0)

        settings_btn = StyledButton("Settings", "text")
        settings_btn.clicked.connect(self.open_settings_dialog)

        cache_btn = StyledButton("Manage Cache", "text")
        cache_btn.clicked.connect(self.open_cache_manager_dialog)

        self.auto_start_check = QCheckBox("Auto-start service on launch")
        self.auto_start_check.setChecked(self.config_manager.get('auto_start', False))
        self.auto_start_check.toggled.connect(self.toggle_auto_start)

        footer_layout.addWidget(settings_btn)
        footer_layout.addWidget(cache_btn)
        footer_layout.addStretch()
        footer_layout.addWidget(self.auto_start_check)
        main_layout.addWidget(footer)

        main_layout.addStretch()

        # Initial refresh of translations and status
        self.refresh_recent_translations()
        self.update_service_status_ui()

    def setup_system_tray(self):
        if not QSystemTrayIcon.isSystemTrayAvailable():
            return

        self.tray_icon = QSystemTrayIcon(self)
        self.tray_icon.setIcon(self.style().standardIcon(QStyle.SP_ComputerIcon))

        tray_menu = QMenu()
        self.start_action_tray = QAction("Start Service", self)
        self.start_action_tray.triggered.connect(self.start_service)
        tray_menu.addAction(self.start_action_tray)

        self.stop_action_tray = QAction("Stop Service", self)
        self.stop_action_tray.triggered.connect(self.stop_service)
        self.stop_action_tray.setEnabled(False)
        tray_menu.addAction(self.stop_action_tray)

        tray_menu.addSeparator()
        settings_action = QAction("Settings", self)
        settings_action.triggered.connect(self.open_settings_dialog)
        tray_menu.addAction(settings_action)

        cache_action = QAction("Manage Cache", self)
        cache_action.triggered.connect(self.open_cache_manager_dialog)
        tray_menu.addAction(cache_action)

        tray_menu.addSeparator()
        quit_action = QAction("Quit", self)
        quit_action.triggered.connect(QApplication.instance().quit)
        tray_menu.addAction(quit_action)

        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.show()
        self.tray_icon.activated.connect(self.on_tray_icon_activated)

    def on_tray_icon_activated(self, reason):
        if reason == QSystemTrayIcon.Trigger or reason == QSystemTrayIcon.DoubleClick:
            self.show()
            self.raise_()
            self.activateWindow()

    def get_service_display_name(self):
        config = self.config_manager.config
        service = config.get('translation_service', 'unknown')
        if service == 'ollama':
            return f"Ollama ({config.get('ollama_model', 'N/A')})"
        elif service == 'gemini':
             return f"Gemini ({config.get('gemini_model', 'N/A')})"
        elif service == 'custom':
            model_name = config.get('custom_model', 'N/A')
            return f"Custom API ({model_name if model_name else 'N/A'})"
        return f"{str(service).capitalize()} (Unknown)"

    def update_service_status_ui(self):
        service_name = self.get_service_display_name()
        is_running = self.clipboard_service is not None and self.clipboard_service.isRunning()

        if is_running:
            self.status_pill.label.setText(f"{service_name} - Running")
            self.status_pill.dot.setProperty("color", "#4CAF50")
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            if hasattr(self, 'tray_icon'):
                self.start_action_tray.setEnabled(False)
                self.stop_action_tray.setEnabled(True)
        else:
            self.status_pill.label.setText(f"{service_name} - Stopped")
            self.status_pill.dot.setProperty("color", "gray")
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            if hasattr(self, 'tray_icon'):
                self.start_action_tray.setEnabled(True)
                self.stop_action_tray.setEnabled(False)
        
        # Re-apply style to update dot color
        self.status_pill.dot.style().unpolish(self.status_pill.dot)
        self.status_pill.dot.style().polish(self.status_pill.dot)

    def toggle_auto_start(self):
        is_checked = self.auto_start_check.isChecked()
        self.config_manager.set('auto_start', is_checked)
        try:
            self.config_manager.save_config(self.config_manager.config)
        except Exception as e:
             QMessageBox.critical(self, "Settings Error", f"Failed to save auto-start setting: {e}")

    def start_service(self):
        if self.clipboard_service is not None and self.clipboard_service.isRunning():
            return

        if self.clipboard_service is not None:
            self.clipboard_service = None

        try:
            self.clipboard_service = ClipboardService(self.config_manager, self.cache_manager)
            self.clipboard_service.translation_requested.connect(self.show_pyside6_translation_prompt)
            self.clipboard_service.translation_done.connect(self.show_pyside6_translation_result)
            self.clipboard_service.error_occurred.connect(self.show_pyside6_error_message)
            self.clipboard_service.finished.connect(self.on_clipboard_service_finished)
            self.clipboard_service.start()
            self.update_service_status_ui()
        except Exception as e:
            QMessageBox.critical(self, "Service Error", f"Failed to start clipboard service: {e}")
            if self.clipboard_service:
                self.clipboard_service = None
            self.update_service_status_ui()

    def stop_service(self):
        if self.clipboard_service is None or not self.clipboard_service.isRunning():
            return

        self.clipboard_service.stop()
        self.clipboard_service.quit()
        if not self.clipboard_service.wait(5000):
            self.clipboard_service.terminate()
            self.clipboard_service.wait(1000)

    def on_clipboard_service_finished(self):
        if self.clipboard_service:
            self.clipboard_service.deleteLater()
            self.clipboard_service = None
        self.update_service_status_ui()
        if hasattr(self, 'tray_icon'):
            self.tray_icon.showMessage("Clipboard Translator", "Service Stopped", QSystemTrayIcon.Information, 2000)

    def refresh_recent_translations(self):
        self.translations_list.clear()
        try:
            results = self.cache_manager.get_recent_translations(limit=20)
            for original, translation, accessed_at, is_structured in results:
                # For display in the list, we still show simple translation
                # The structured info is used in the detailed result window
                item = TranslationListItem(
                    original[:50] + '...' if len(original) > 50 else original,
                    translation[:50] + '...' if len(translation) > 50 else translation,
                    accessed_at[:19] + (" (S)" if is_structured else "") # Add (S) for structured
                )
                self.translations_list.addItem(item)
                self.translations_list.setItemWidget(item, item.widget)
        except Exception as e:
            QMessageBox.warning(self, "Cache Error", f"Recent translations error: {e}")

    @Slot(str)
    def show_pyside6_translation_prompt(self, text: str):
        from ui.custom_prompt_window import CustomPromptWindow
        cached_translation = self.cache_manager.get_cached_translation(text)
        cache_info = " (Cached)" if cached_translation else ""
        self.prompt_window = CustomPromptWindow(text, cache_info)
        self.prompt_window.user_responded.connect(self.on_prompt_response)
        if self.isVisible():
            self.prompt_window.move(self.geometry().center() - self.prompt_window.rect().center())
        self.prompt_window.show()

    def on_prompt_response(self, translate: bool):
        if translate:
            text_to_translate = self.prompt_window.text_to_translate
            if self.clipboard_service and self.clipboard_service.isRunning():
                self.clipboard_service.translate_text(text_to_translate)
        self.prompt_window = None

    @Slot(str, str, bool, str)
    def show_pyside6_translation_result(self, original: str, translation: str, from_cache: bool, structured_response_json_str: str):
        from ui.custom_result_window import CustomResultWindow
        self.refresh_recent_translations()
        self.result_window = CustomResultWindow(original, translation, from_cache, structured_response_json_str)
        self.result_window.show()
        if hasattr(self, 'tray_icon'):
            self.tray_icon.showMessage(
                "Translation Complete",
                f"Original: {original[:30]}...",
                QSystemTrayIcon.Information,
                3000
            )

    @Slot(str)
    def show_pyside6_error_message(self, message: str):
        QMessageBox.critical(self, "Clipboard Translator Error", message)

    def open_settings_dialog(self):
        settings_window = SettingsWindow(self.config_manager, self)
        settings_window.config_updated.connect(self.on_config_updated)
        settings_window.exec()

    def on_config_updated(self, new_config):
        self.update_service_status_ui()
        if self.clipboard_service and self.clipboard_service.isRunning():
            self.stop_service()
            QTimer.singleShot(1000, self.start_service)

    def open_cache_manager_dialog(self):
        QMessageBox.information(self, "Cache Manager", "Cache manager dialog is not yet implemented.")

    def closeEvent(self, event):
        if self.clipboard_service is not None and self.clipboard_service.isRunning():
            self.stop_service()
            if self.clipboard_service and not self.clipboard_service.wait(2000):
                pass
        if hasattr(self, 'tray_icon'):
            self.tray_icon.hide()
        event.accept()
