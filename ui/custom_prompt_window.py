

# ui/custom_prompt_window.py
from PySide6.QtWidgets import QWidget, QLabel, QVBoxLayout, QHBoxLayout, QFrame
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont
from ui.widgets import StyledButton
from ui.design_system import Colors, Typography, Spacing, ComponentStyles

class CustomPromptWindow(QWidget):
    user_responded = Signal(bool)

    def __init__(self, text, cache_info, parent=None):
        super().__init__(parent)
        self.text_to_translate = text

        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_DeleteOnClose)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        container = QWidget()
        container.setObjectName("CustomPromptWindowContainer")

        # Apply modern styling with gradient background
        container.setStyleSheet(f"""
            #CustomPromptWindowContainer {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {Colors.BG_PRIMARY.name()},
                    stop:1 {Colors.BG_SECONDARY.name()});
                border: 1px solid {Colors.NEUTRAL_100.name()};
                border-radius: 16px;
                color: {Colors.TEXT_PRIMARY.name()};
            }}
        """)

        container_layout = QVBoxLayout(container)
        container_layout.setContentsMargins(Spacing.CONTAINER, Spacing.CONTAINER, Spacing.CONTAINER, Spacing.CONTAINER)
        container_layout.setSpacing(Spacing.SECTION)

        # Title
        title_label = QLabel("Translation Request")
        title_label.setFont(Typography.create_font(Typography.SIZE_HEADING, Typography.WEIGHT_BOLD))
        title_label.setStyleSheet(f"color: {Colors.TEXT_PRIMARY.name()}; margin-bottom: {Spacing.MD}px;")
        title_label.setAlignment(Qt.AlignCenter)
        container_layout.addWidget(title_label)

        # Text preview
        text_preview = text[:150] + "..." if len(text) > 150 else text
        prompt_label = QLabel(f"Translate this text?\n\n{text_preview}{cache_info}")
        prompt_label.setFont(Typography.create_font(Typography.SIZE_BODY))
        prompt_label.setStyleSheet(f"color: {Colors.TEXT_SECONDARY.name()}; line-height: 1.4;")
        prompt_label.setWordWrap(True)
        prompt_label.setAlignment(Qt.AlignCenter)
        container_layout.addWidget(prompt_label)

        button_layout = QHBoxLayout()
        button_layout.setSpacing(Spacing.MD)
        button_layout.addStretch()

        no_btn = StyledButton("Cancel", "ghost")
        yes_btn = StyledButton("Translate", "primary")

        yes_btn.clicked.connect(self.accept_translation)
        no_btn.clicked.connect(self.reject_translation)

        button_layout.addWidget(no_btn)
        button_layout.addWidget(yes_btn)
        container_layout.addLayout(button_layout)

        layout.addWidget(container)

    def accept_translation(self):
        self.user_responded.emit(True)
        self.close()

    def reject_translation(self):
        self.user_responded.emit(False)
        self.close()

