# -*- coding: utf-8 -*-
"""
Modern Design System

This module defines the design system extracted from the beautiful audio wave visualizer
and provides consistent styling, colors, and components for the entire application.

Features:
- Modern color palette with semantic naming
- Gradient definitions and utilities
- Typography system
- Spacing and sizing constants
- Component styling patterns
- Animation and transition definitions
"""

from PySide6.QtGui import QColor, QLinearGradient, QFont
from PySide6.QtCore import Qt
from typing import Dict, <PERSON><PERSON>, Any


class Colors:
    """Modern color palette based on the audio wave visualizer design."""
    
    # Primary Background Colors
    BG_PRIMARY = QColor(30, 41, 59)      # #1e293b - Main background
    BG_SECONDARY = QColor(15, 23, 42)    # #0f172a - Secondary background
    BG_SURFACE = QColor(30, 41, 59, 200) # #1e293b with alpha - Panel background
    
    # Text Colors
    TEXT_PRIMARY = QColor(255, 255, 255)    # #ffffff - Primary text
    TEXT_SECONDARY = QColor(148, 163, 184)  # #94a3b8 - Secondary text
    TEXT_MUTED = QColor(100, 116, 139)      # #64748b - Muted text
    
    # Accent Colors (from audio visualizer)
    ACCENT_BLUE = QColor(96, 165, 250)      # #60a5fa - Primary accent
    ACCENT_PURPLE = QColor(99, 102, 241)    # #6366f1 - Secondary accent
    ACCENT_PURPLE_LIGHT = QColor(129, 140, 248)  # #818cf8 - Light purple
    
    # Status Colors
    SUCCESS = QColor(16, 185, 129)          # #10b981 - Success/transcribed
    SUCCESS_DARK = QColor(5, 150, 105)      # #059669 - Success border
    ERROR = QColor(239, 68, 68)             # #ef4444 - Error/playhead
    ERROR_DARK = QColor(220, 38, 38)        # #dc2626 - Error hover
    WARNING = QColor(245, 158, 11)          # #f59e0b - Warning
    
    # Interactive Colors
    INTERACTIVE_HOVER = QColor(37, 99, 235)  # #2563eb - Hover state
    INTERACTIVE_PRESSED = QColor(29, 78, 216) # #1d4ed8 - Pressed state
    
    # Neutral Colors
    NEUTRAL_100 = QColor(71, 85, 105)       # #475569 - Light neutral
    NEUTRAL_200 = QColor(51, 65, 85)        # #334155 - Medium neutral
    NEUTRAL_300 = QColor(30, 41, 59)        # #1e293b - Dark neutral
    
    # Selection and Overlay Colors
    SELECTION_FILL = QColor(99, 102, 241, 51)    # rgba(99, 102, 241, 0.2)
    TRANSCRIBED_FILL = QColor(16, 185, 129, 100) # rgba(16, 185, 129, 0.4)
    OVERLAY_LIGHT = QColor(255, 255, 255, 25)    # rgba(255, 255, 255, 0.1)
    OVERLAY_DARK = QColor(0, 0, 0, 51)           # rgba(0, 0, 0, 0.2)


class Gradients:
    """Gradient definitions for modern visual effects."""
    
    @staticmethod
    def create_background_gradient() -> QLinearGradient:
        """Create the main background gradient used in the audio visualizer."""
        gradient = QLinearGradient(0, 0, 0, 1)  # Vertical gradient
        gradient.setColorAt(0, Colors.BG_PRIMARY)
        gradient.setColorAt(1, Colors.BG_SECONDARY)
        gradient.setCoordinateMode(QLinearGradient.ObjectBoundingMode)
        return gradient
    
    @staticmethod
    def create_button_gradient(base_color: QColor) -> QLinearGradient:
        """Create a subtle gradient for buttons."""
        gradient = QLinearGradient(0, 0, 0, 1)
        gradient.setColorAt(0, base_color.lighter(110))
        gradient.setColorAt(1, base_color.darker(110))
        gradient.setCoordinateMode(QLinearGradient.ObjectBoundingMode)
        return gradient
    
    @staticmethod
    def create_panel_gradient() -> QLinearGradient:
        """Create a gradient for panels and cards."""
        gradient = QLinearGradient(0, 0, 0, 1)
        gradient.setColorAt(0, Colors.BG_SURFACE)
        gradient.setColorAt(1, Colors.BG_SURFACE.darker(105))
        gradient.setCoordinateMode(QLinearGradient.ObjectBoundingMode)
        return gradient


class Typography:
    """Typography system with consistent font definitions."""
    
    # Font families
    FONT_FAMILY = "Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif"
    
    # Font sizes
    SIZE_TITLE = 24
    SIZE_SUBTITLE = 18
    SIZE_HEADING = 16
    SIZE_BODY = 14
    SIZE_CAPTION = 12
    SIZE_SMALL = 10
    
    # Font weights
    WEIGHT_LIGHT = QFont.Light
    WEIGHT_NORMAL = QFont.Normal
    WEIGHT_MEDIUM = QFont.Medium
    WEIGHT_BOLD = QFont.Bold
    
    @staticmethod
    def create_font(size: int, weight: QFont.Weight = QFont.Normal) -> QFont:
        """Create a font with the design system specifications."""
        font = QFont()
        font.setFamily(Typography.FONT_FAMILY)
        font.setPointSize(size)
        font.setWeight(weight)
        return font


class Spacing:
    """Consistent spacing system."""
    
    # Base spacing unit (4px)
    UNIT = 4
    
    # Common spacing values
    XS = UNIT          # 4px
    SM = UNIT * 2      # 8px
    MD = UNIT * 3      # 12px
    LG = UNIT * 4      # 16px
    XL = UNIT * 5      # 20px
    XXL = UNIT * 6     # 24px
    
    # Layout spacing
    SECTION = UNIT * 4    # 16px
    PANEL = UNIT * 5      # 20px
    CONTAINER = UNIT * 6  # 24px


class BorderRadius:
    """Border radius system for consistent rounded corners."""
    
    SM = 4    # Small radius
    MD = 8    # Medium radius
    LG = 12   # Large radius
    XL = 16   # Extra large radius
    ROUND = 50  # Fully rounded (for circular elements)


class Shadows:
    """Shadow definitions for depth and elevation."""
    
    # Shadow colors
    SHADOW_LIGHT = QColor(0, 0, 0, 30)   # Light shadow
    SHADOW_MEDIUM = QColor(0, 0, 0, 60)  # Medium shadow
    SHADOW_DARK = QColor(0, 0, 0, 100)   # Dark shadow
    
    # Shadow parameters (blur_radius, offset_x, offset_y)
    CARD = (20, 0, 4)      # Card shadow
    BUTTON = (10, 0, 2)    # Button shadow
    PANEL = (15, 0, 3)     # Panel shadow


class Animations:
    """Animation and transition definitions."""
    
    # Duration constants (in milliseconds)
    FAST = 150
    NORMAL = 300
    SLOW = 500
    
    # Easing curves
    EASE_IN_OUT = "ease-in-out"
    EASE_OUT = "ease-out"
    EASE_IN = "ease-in"


class ComponentStyles:
    """Pre-defined component styling patterns."""
    
    @staticmethod
    def get_button_style(variant: str = "primary") -> str:
        """Get button styling based on variant."""
        base_style = f"""
            QPushButton {{
                font-family: {Typography.FONT_FAMILY};
                font-size: {Typography.SIZE_BODY}px;
                font-weight: 600;
                border: none;
                border-radius: {BorderRadius.MD}px;
                padding: {Spacing.SM}px {Spacing.LG}px;
                min-height: 36px;
            }}
        """
        
        if variant == "primary":
            return base_style + f"""
                QPushButton {{
                    background-color: {Colors.ACCENT_BLUE.name()};
                    color: {Colors.TEXT_PRIMARY.name()};
                }}
                QPushButton:hover {{
                    background-color: {Colors.INTERACTIVE_HOVER.name()};
                }}
                QPushButton:pressed {{
                    background-color: {Colors.INTERACTIVE_PRESSED.name()};
                }}
                QPushButton:disabled {{
                    background-color: {Colors.NEUTRAL_100.name()};
                    color: {Colors.TEXT_MUTED.name()};
                }}
            """
        elif variant == "secondary":
            return base_style + f"""
                QPushButton {{
                    background-color: {Colors.SUCCESS.name()};
                    color: {Colors.TEXT_PRIMARY.name()};
                }}
                QPushButton:hover {{
                    background-color: {Colors.SUCCESS_DARK.name()};
                }}
                QPushButton:disabled {{
                    background-color: {Colors.NEUTRAL_100.name()};
                    color: {Colors.TEXT_MUTED.name()};
                }}
            """
        elif variant == "danger":
            return base_style + f"""
                QPushButton {{
                    background-color: {Colors.ERROR.name()};
                    color: {Colors.TEXT_PRIMARY.name()};
                }}
                QPushButton:hover {{
                    background-color: {Colors.ERROR_DARK.name()};
                }}
                QPushButton:disabled {{
                    background-color: {Colors.NEUTRAL_100.name()};
                    color: {Colors.TEXT_MUTED.name()};
                }}
            """
        elif variant == "ghost":
            return base_style + f"""
                QPushButton {{
                    background-color: transparent;
                    color: {Colors.TEXT_SECONDARY.name()};
                    border: 1px solid {Colors.NEUTRAL_100.name()};
                }}
                QPushButton:hover {{
                    background-color: {Colors.OVERLAY_LIGHT.name()};
                    color: {Colors.TEXT_PRIMARY.name()};
                }}
                QPushButton:disabled {{
                    color: {Colors.TEXT_MUTED.name()};
                    border-color: {Colors.TEXT_MUTED.name()};
                }}
            """
        
        return base_style
    
    @staticmethod
    def get_panel_style() -> str:
        """Get panel/card styling."""
        return f"""
            QFrame {{
                background-color: {Colors.BG_SURFACE.name()};
                border: 1px solid {Colors.NEUTRAL_100.name()};
                border-radius: {BorderRadius.LG}px;
                padding: {Spacing.PANEL}px;
            }}
        """
    
    @staticmethod
    def get_input_style() -> str:
        """Get input field styling."""
        return f"""
            QLineEdit, QTextEdit, QSpinBox {{
                background-color: {Colors.BG_SECONDARY.name()};
                border: 1px solid {Colors.NEUTRAL_100.name()};
                border-radius: {BorderRadius.SM}px;
                padding: {Spacing.SM}px {Spacing.MD}px;
                color: {Colors.TEXT_PRIMARY.name()};
                font-family: {Typography.FONT_FAMILY};
                font-size: {Typography.SIZE_BODY}px;
            }}
            QLineEdit:focus, QTextEdit:focus, QSpinBox:focus {{
                border-color: {Colors.ACCENT_BLUE.name()};
                outline: none;
            }}
        """


# Export the design system components
__all__ = [
    'Colors', 'Gradients', 'Typography', 'Spacing', 
    'BorderRadius', 'Shadows', 'Animations', 'ComponentStyles'
]
