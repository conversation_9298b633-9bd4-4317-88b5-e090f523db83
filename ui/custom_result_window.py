# ui/custom_result_window.py

import json
from PySide6.QtWidgets import (
    QWidget, QLabel, QVB<PERSON>Layout, QHBoxLayout, QApplication,
    QScrollArea, QFrame, QTextBrowser, QSplitter
)
from PySide6.QtCore import Qt, Signal, QUrl, Slot
from ui.widgets import StyledButton
from ui.design_system import Colors, Typography, Spacing, ComponentStyles
from services.translation.translation_models import TranslationResponse, CommonExpression, WordBreakdown

class CustomResultWindow(QWidget):
    close_requested = Signal()
    re_translate_requested = Signal(str)

    def __init__(self, original: str, translation: str, from_cache: bool, structured_response_json_str: str, parent=None):
        super().__init__(parent)
        self.original_text = original
        self.primary_translation = translation
        self.from_cache = from_cache
        self.current_untranslated_segment = None
        
        self.data_store = []
        self.interactive_segments = []

        if structured_response_json_str:
            try:
                self.structured_response = TranslationResponse.from_json(structured_response_json_str)
            except (json.JSONDecodeError, TypeError):
                self.structured_response = None
        else:
            self.structured_response = None
        
        # --- Run the new, robust segmentation logic ---
        self._prepare_interactive_segments()

        # --- Modern Window Setup ---
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_DeleteOnClose)
        self.setMinimumSize(900, 600)

        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        container = QFrame()
        container.setObjectName("CustomResultWindowContainer")

        # Apply modern styling with gradient background
        container.setStyleSheet(f"""
            #CustomResultWindowContainer {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {Colors.BG_PRIMARY.name()},
                    stop:1 {Colors.BG_SECONDARY.name()});
                border: 1px solid {Colors.NEUTRAL_100.name()};
                border-radius: 16px;
                color: {Colors.TEXT_PRIMARY.name()};
            }}
        """)

        container_layout = QVBoxLayout(container)
        container_layout.setContentsMargins(Spacing.CONTAINER, Spacing.CONTAINER, Spacing.CONTAINER, Spacing.CONTAINER)
        container_layout.setSpacing(Spacing.SECTION)
        main_layout.addWidget(container)
        # Title with modern styling
        title_text = f"Translation Result{' (from cache)' if self.from_cache else ''}"
        title_label = QLabel(title_text)
        title_label.setFont(Typography.create_font(Typography.SIZE_TITLE, Typography.WEIGHT_BOLD))
        title_label.setStyleSheet(f"""
            color: {Colors.TEXT_PRIMARY.name()};
            padding-bottom: {Spacing.MD}px;
            border-bottom: 1px solid {Colors.NEUTRAL_100.name()};
            margin-bottom: {Spacing.LG}px;
        """)
        container_layout.addWidget(title_label)

        # Splitter with modern styling
        self.splitter = QSplitter(Qt.Horizontal)
        self.splitter.setChildrenCollapsible(False)
        self.splitter.setStyleSheet(f"""
            QSplitter::handle {{
                background-color: {Colors.NEUTRAL_100.name()};
                width: 2px;
            }}
            QSplitter::handle:hover {{
                background-color: {Colors.ACCENT_BLUE.name()};
            }}
        """)
        container_layout.addWidget(self.splitter)

        left_pane = self._create_left_pane()
        right_pane = self._create_right_pane()
        self.splitter.addWidget(left_pane)
        self.splitter.addWidget(right_pane)
        self.splitter.setStretchFactor(0, 1)
        self.splitter.setStretchFactor(1, 2)

        # Modern button layout
        button_layout = QHBoxLayout()
        button_layout.setSpacing(Spacing.MD)
        button_layout.addStretch()

        copy_btn = StyledButton("Copy Translation", "ghost")
        copy_btn.clicked.connect(self.copy_primary_translation)

        close_btn = StyledButton("Close", "primary")
        close_btn.clicked.connect(self.close_requested.emit)

        button_layout.addWidget(copy_btn)
        button_layout.addWidget(close_btn)
        container_layout.addLayout(button_layout)

    # --- REWRITTEN: The new, robust "character map" algorithm ---
    def _prepare_interactive_segments(self):
        """
        Processes the original text using a robust "character map" approach
        to prevent segmentation and indexing errors.
        """
        if not self.original_text:
            return

        text_len = len(self.original_text)
        # Initialize a map for each character, defaulting to 'plain'.
        # The 'data' will be filled in as we go.
        char_map = [{'type': 'plain', 'data': None} for _ in range(text_len)]

        # --- Pass 1: Mark Common Expressions (longest first to get precedence) ---
        if self.structured_response and self.structured_response.common_expressions:
            sorted_expressions = sorted(self.structured_response.common_expressions, key=lambda x: len(x.expression), reverse=True)
            for expr in sorted_expressions:
                start_index = 0
                while True:
                    try:
                        pos = self.original_text.index(expr.expression, start_index)
                        # Only mark if the section is currently unmarked ('plain')
                        is_free = all(char_map[i]['type'] == 'plain' for i in range(pos, pos + len(expr.expression)))
                        if is_free:
                            for i in range(pos, pos + len(expr.expression)):
                                char_map[i] = {'type': 'expression', 'data': expr}
                        start_index = pos + len(expr.expression)
                    except ValueError:
                        break # No more occurrences of this expression

        # --- Pass 2: Mark individual words in remaining 'plain' sections ---
        if self.structured_response and self.structured_response.word_breakdown:
            word_map = {word.character: word for word in self.structured_response.word_breakdown}
            for i in range(text_len):
                if char_map[i]['type'] == 'plain':
                    char = self.original_text[i]
                    if char in word_map:
                        char_map[i] = {'type': 'word', 'data': word_map[char]}

        # --- Pass 3: Consolidate the character map into final segments ---
        if not char_map:
            return
        
        final_segments = []
        i = 0
        while i < text_len:
            char_info = char_map[i]
            current_type = char_info['type']
            current_data = char_info['data']

            if current_type == 'expression':
                segment_text = current_data.expression
                segment_len = len(segment_text)
                final_segments.append({'text': segment_text, 'type': 'expression', 'data': current_data})
                i += segment_len
            elif current_type == 'word':
                segment_text = current_data.character
                segment_len = len(segment_text)
                final_segments.append({'text': segment_text, 'type': 'word', 'data': current_data})
                i += segment_len
            else: # type == 'plain'
                j = i
                while j < text_len and char_map[j]['type'] == 'plain':
                    j += 1
                segment_text = self.original_text[i:j]
                segment_len = len(segment_text)
                # For plain text, the data is the text itself for re-translation
                final_segments.append({'text': segment_text, 'type': 'plain', 'data': segment_text})
                i += segment_len

        # --- Final Pass: Build the data_store and indexed segments for the UI ---
        self.data_store.clear()
        self.interactive_segments.clear()
        for segment in final_segments:
            self.data_store.append(segment['data'])
            index = len(self.data_store) - 1
            self.interactive_segments.append((segment['text'], segment['type'], index))

    @Slot(QUrl)
    def _on_word_clicked(self, url: QUrl):
        """Handles clicks from the QTextBrowser using a reliable URL parsing method."""
        url_str = url.toString()
        if not url_str.startswith("data://"):
            return
            
        try:
            index_str = url_str.split("://")[1]
            index = int(index_str)
            data = self.data_store[index]
        except (ValueError, IndexError):
            self.details_label.setText("<p>Error: Invalid data link clicked.</p>")
            return

        self.re_translate_btn.hide()
        
        if isinstance(data, CommonExpression):
            self._display_expression_details(data)
        elif isinstance(data, WordBreakdown):
            self._display_word_details(data)
        elif isinstance(data, str): # Plain text segment
            self.current_untranslated_segment = data
            self.details_label.setText(f"<p>No detailed breakdown for '<b>{data}</b>'.</p>")
            self.re_translate_btn.setText(f"Translate '{data}'")
            self.re_translate_btn.show()

    # --- The rest of the file is identical to the previous version ---
    # It will now work correctly with the properly generated data.
            
    def _generate_interactive_html(self) -> str:
        html_parts = ["""
        <html><head><style>
            a { text-decoration: none; font-weight: bold; }
            a.expression { color: #87CEFA; text-decoration: underline; }
            a.word { color: #98FB98; }
            a.plain { color: #a9a9a9; font-weight: normal; }
            a:hover { background-color: rgba(255, 255, 255, 0.2); }
        </style></head>
        <body style='font-size: 18px; color: #EAEAEA; word-break: break-all; line-height: 1.6;'>
        """]
        for text, type, data_index in self.interactive_segments:
            html_parts.append(f'<a href="data://{data_index}" class="{type}">{text}</a>')
        html_parts.append("</body></html>")
        return "".join(html_parts)

    def _display_expression_details(self, expr: CommonExpression):
        examples_html = "".join([f"<li><i>{ex}</i></li>" for ex in expr.examples])
        details_text = f"""
            <p><b>Expression:</b> {expr.expression}</p>
            <p><b>Meaning:</b> {expr.meaning}</p>
            <p><b>Reading:</b> {expr.reading}</p>
            <p><b>Context:</b> {expr.context}</p>
            <p><b>Examples:</b></p>
            <ul>{examples_html}</ul>
        """
        self.details_label.setText(details_text)

    def _display_word_details(self, word: WordBreakdown):
        details_text = f"""
            <p><b>Character:</b> {word.character}</p>
            <p><b>Meaning:</b> {word.meaning or 'N/A'}</p>
            <p><b>Reading:</b> {word.reading or 'N/A'}</p>
            <p><b>Part of Speech:</b> {word.part_of_speech or 'N/A'}</p>
        """
        self.details_label.setText(details_text)
    
    def _on_re_translate_clicked(self):
        if self.current_untranslated_segment:
            self.re_translate_requested.emit(self.current_untranslated_segment)
            self.details_label.setText(f"<p>Requesting translation for '<b>{self.current_untranslated_segment}</b>'...</p>")
            self.re_translate_btn.hide()

    def _create_right_pane(self):
        right_pane_widget = QWidget()
        layout = QVBoxLayout(right_pane_widget)
        layout.setSpacing(15)
        self._add_section_header("Interactive Original Text", layout)
        self.interactive_text_browser = QTextBrowser()
        self.interactive_text_browser.setOpenLinks(False)
        self.interactive_text_browser.anchorClicked.connect(self._on_word_clicked)
        self.interactive_text_browser.setObjectName("InteractiveTextBrowser")
        self.interactive_text_browser.setHtml(self._generate_interactive_html())
        layout.addWidget(self.interactive_text_browser)
        self._add_section_header("Selection Details", layout)
        self.details_frame = QFrame()
        self.details_frame.setObjectName("DetailsFrame")
        details_layout = QVBoxLayout(self.details_frame)
        self.details_label = QLabel("Click on a highlighted phrase or word to see its details.")
        self.details_label.setTextFormat(Qt.RichText)
        self.details_label.setWordWrap(True)
        self.details_label.setObjectName("DetailsPlaceholderLabel")
        details_layout.addWidget(self.details_label)
        self.re_translate_btn = StyledButton("Translate ''", "primary")
        self.re_translate_btn.clicked.connect(self._on_re_translate_clicked)
        self.re_translate_btn.hide()
        details_layout.addWidget(self.re_translate_btn, 0, Qt.AlignLeft)
        layout.addWidget(self.details_frame, 1)
        return right_pane_widget
        
    def _create_left_pane(self):
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        scroll_content = QWidget()
        layout = QVBoxLayout(scroll_content)
        layout.setSpacing(15)
        self._add_info_card("Primary Translation", self.primary_translation, layout)
        if self.structured_response and self.structured_response.alternative_translations:
            alt_text = "".join([f"<li><b>{alt.translation}</b>: <i>{alt.context}</i></li>" 
                                for alt in self.structured_response.alternative_translations])
            self._add_info_card("Alternatives", f"<ul>{alt_text}</ul>", layout)
        if self.structured_response and self.structured_response.grammar_notes:
            grammar_text = "".join([f"<li><b>{note.pattern}</b>: {note.explanation}</li>" 
                                    for note in self.structured_response.grammar_notes])
            self._add_info_card("Grammar Notes", f"<ul>{grammar_text}</ul>", layout)
        layout.addStretch()
        scroll_area.setWidget(scroll_content)
        return scroll_area

    def _add_info_card(self, title_text, content_text, parent_layout):
        card = QFrame()
        card.setObjectName("InfoCard")
        card_layout = QVBoxLayout(card)
        title = QLabel(title_text)
        title.setObjectName("SectionSubTitle")
        content = QLabel(content_text)
        content.setWordWrap(True)
        content.setTextFormat(Qt.RichText)
        content.setOpenExternalLinks(False)
        card_layout.addWidget(title)
        card_layout.addWidget(content)
        parent_layout.addWidget(card)
    
    def _add_section_header(self, title: str, layout):
        header_label = QLabel(title)
        header_label.setObjectName("SectionTitle")
        layout.addWidget(header_label)

    def copy_primary_translation(self):
        QApplication.clipboard().setText(self.primary_translation)